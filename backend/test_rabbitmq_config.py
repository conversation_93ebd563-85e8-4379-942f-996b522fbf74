#!/usr/bin/env python3
"""
Test script to verify RabbitMQ configuration logic
"""
import os
import sys

# Mock the required modules for testing
class MockPika:
    class PlainCredentials:
        def __init__(self, username, password):
            self.username = username
            self.password = password
            print(f"Created credentials: {username}:{password}")

class MockDramatiq:
    class middleware:
        class AsyncIO:
            pass

class MockRabbitmqBroker:
    def __init__(self, **kwargs):
        print("RabbitmqBroker initialized with:")
        for key, value in kwargs.items():
            if key == 'credentials':
                print(f"  {key}: {value.username}:{value.password}")
            else:
                print(f"  {key}: {value}")

# Mock the imports
sys.modules['pika'] = MockPika()
sys.modules['dramatiq'] = MockDramatiq()
sys.modules['dramatiq.middleware'] = MockDramatiq.middleware
sys.modules['dramatiq.brokers.rabbitmq'] = type('MockModule', (), {'RabbitmqBroker': MockRabbitmqBroker})()

def test_rabbitmq_config():
    """Test the RabbitMQ configuration logic"""
    print("Testing RabbitMQ configuration...")
    
    # Test with URL (production scenario)
    print("\n1. Testing with RABBITMQ_URL (production):")
    os.environ['RABBITMQ_URL'] = 'amqp://user:pass@host:5672/vhost'
    
    # Clear individual parameters
    for key in ['RABBITMQ_HOST', 'RABBITMQ_PORT', 'RABBITMQ_USER', 'RABBITMQ_PASS', 'RABBITMQ_VHOST']:
        os.environ.pop(key, None)
    
    # Simulate the configuration logic
    rabbitmq_url = os.getenv("RABBITMQ_URL")
    if rabbitmq_url:
        print(f"Using URL: {rabbitmq_url}")
        broker_config = {'url': rabbitmq_url, 'middleware': []}
        print("Would create broker with URL configuration")
    
    # Test with individual parameters (local scenario)
    print("\n2. Testing with individual parameters (local):")
    os.environ.pop('RABBITMQ_URL', None)
    os.environ.update({
        'RABBITMQ_HOST': 'rabbitmq',
        'RABBITMQ_PORT': '5672',
        'RABBITMQ_USER': 'guest',
        'RABBITMQ_PASS': 'guest',
        'RABBITMQ_VHOST': '/'
    })
    
    # Simulate the configuration logic
    rabbitmq_url = os.getenv("RABBITMQ_URL")
    if not rabbitmq_url:
        rabbitmq_host = os.getenv("RABBITMQ_HOST", "rabbitmq")
        rabbitmq_port = int(os.getenv("RABBITMQ_PORT", 5672))
        rabbitmq_user = os.getenv("RABBITMQ_USER", "guest")
        rabbitmq_pass = os.getenv("RABBITMQ_PASS", "guest")
        rabbitmq_vhost = os.getenv("RABBITMQ_VHOST", "/")
        
        print(f"Host: {rabbitmq_host}")
        print(f"Port: {rabbitmq_port}")
        print(f"User: {rabbitmq_user}")
        print(f"Pass: {rabbitmq_pass}")
        print(f"VHost: {rabbitmq_vhost}")
        
        # Create credentials object
        credentials = MockPika.PlainCredentials(rabbitmq_user, rabbitmq_pass)
        
        # Create broker
        broker = MockRabbitmqBroker(
            host=rabbitmq_host,
            port=rabbitmq_port,
            credentials=credentials,
            virtual_host=rabbitmq_vhost,
            middleware=[]
        )
    
    print("\n✅ RabbitMQ configuration test completed successfully!")

if __name__ == "__main__":
    test_rabbitmq_config()
